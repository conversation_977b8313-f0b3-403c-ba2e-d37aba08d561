"use strict";var G=Object.defineProperty;var J=(t,s,e)=>s in t?G(t,s,{enumerable:!0,configurable:!0,writable:!0,value:e}):t[s]=e;var R=(t,s,e)=>(J(t,typeof s!="symbol"?s+"":s,e),e);const c=require("electron"),z=require("os"),h=require("path"),D=require("express"),W=require("cors"),i=require("sequelize"),_=require("fs"),X=require("sql.js");class Y{constructor(s){R(this,"SQL");R(this,"db");R(this,"dbPath");this.dbPath=s||h.join(c.app.getPath("userData"),"hiltonmarket.db")}async init(){try{console.log("🔄 正在初始化SQLite数据库...");const s=Date.now();if(console.log("📦 加载sql.js WebAssembly模块..."),this.SQL=await X({}),console.log(`✅ sql.js加载完成 (${Date.now()-s}ms)`),_.existsSync(this.dbPath)){console.log("📂 加载现有SQLite数据库文件");const e=_.readFileSync(this.dbPath);this.db=new this.SQL.Database(e)}else console.log("🆕 创建新的SQLite数据库"),this.db=new this.SQL.Database;this.createTables(),console.log(`✅ SQLite数据库初始化完成 (总耗时: ${Date.now()-s}ms)`)}catch(s){throw console.error("❌ SQLite初始化失败:",s),s}}async createTables(){const s=[`CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT UNIQUE NOT NULL,
        email TEXT UNIQUE NOT NULL,
        role TEXT NOT NULL DEFAULT 'user',
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,`CREATE TABLE IF NOT EXISTS hotel_basic_info (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        label TEXT NOT NULL,
        value TEXT NOT NULL,
        note TEXT,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,`CREATE TABLE IF NOT EXISTS hotel_budget_info (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        label TEXT NOT NULL,
        value TEXT NOT NULL,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,`CREATE TABLE IF NOT EXISTS hotel_usp_info (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        category TEXT NOT NULL,
        items TEXT NOT NULL, -- JSON字符串
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )`];for(const e of s)this.db.run(e)}query(s,e=[]){try{const o=this.db.prepare(s).getAsObject(e);return Array.isArray(o)?o:[o]}catch(a){throw console.error("SQLite查询错误:",a),a}}all(s,e=[]){try{const a=this.db.prepare(s),o=[];for(;a.step();)o.push(a.getAsObject());return a.free(),o}catch(a){throw console.error("SQLite查询错误:",a),a}}get(s,e=[]){const a=this.all(s,e);return a.length>0?a[0]:null}run(s,e=[]){var a,o;try{const n=this.db.prepare(s);n.run(e);const r={lastID:((o=(a=this.db.exec("SELECT last_insert_rowid() as id")[0])==null?void 0:a.values[0])==null?void 0:o[0])||0,changes:this.db.getRowsModified()};return n.free(),r}catch(n){throw console.error("SQLite执行错误:",n),n}}save(){try{const s=this.db.export();_.writeFileSync(this.dbPath,s),console.log("💾 SQLite数据库已保存到文件")}catch(s){throw console.error("❌ 保存SQLite数据库失败:",s),s}}close(){try{this.db&&(this.save(),this.db.close(),console.log("✅ SQLite数据库已关闭"))}catch(s){console.error("❌ 关闭SQLite数据库失败:",s)}}async initializeData(){var s;try{console.log("🔍 检查SQLite数据初始化状态...");const e=Date.now();if((((s=this.get("SELECT COUNT(*) as count FROM users"))==null?void 0:s.count)||0)===0){console.log("📝 开始初始化SQLite默认数据..."),this.db.exec("BEGIN TRANSACTION");try{const o=["INSERT INTO users (username, email, role) VALUES ('admin', '<EMAIL>', 'admin')","INSERT INTO users (username, email, role) VALUES ('user', '<EMAIL>', 'user')"],n=["INSERT INTO hotel_basic_info (label, value, note) VALUES ('计划制定人', 'Alice', '用户手填')","INSERT INTO hotel_basic_info (label, value, note) VALUES ('酒店In Code', 'AOGCN', 'incode预处理数据')","INSERT INTO hotel_basic_info (label, value, note) VALUES ('酒店名称', '九寨沟康莱德酒店', '预填与incode匹配')","INSERT INTO hotel_basic_info (label, value, note) VALUES ('酒店所在区域', '西区', '预填与incode匹配')","INSERT INTO hotel_basic_info (label, value, note) VALUES ('总经理', 'A', '用户手填')","INSERT INTO hotel_basic_info (label, value, note) VALUES ('商务总监', 'B', '用户手填')","INSERT INTO hotel_basic_info (label, value, note) VALUES ('市场总监', 'C', '用户手填')","INSERT INTO hotel_basic_info (label, value, note) VALUES ('MECC 联系人', 'D', '用户手填')"],r=["INSERT INTO hotel_budget_info (label, value) VALUES ('酒店本地活动预算', '¥620,125.00')","INSERT INTO hotel_budget_info (label, value) VALUES ('集团市场共享费（Co-op Fund）', '¥349,561.33')","INSERT INTO hotel_budget_info (label, value) VALUES ('PMP', '¥60,000.00')","INSERT INTO hotel_budget_info (label, value) VALUES ('总预算', '¥1,029,686.33')"],u=[`INSERT INTO hotel_usp_info (category, items) VALUES ('rooms', '${JSON.stringify(["家庭房","景观房","独特风格房型","亲子房"])}')`,`INSERT INTO hotel_usp_info (category, items) VALUES ('dining', '${JSON.stringify(["免费早餐","餐厅拥有佳景","国际美食"])}')`,`INSERT INTO hotel_usp_info (category, items) VALUES ('meeting', '${JSON.stringify(["1000平米无柱宴会厅","40平米高清LED","10种会议室组合"])}')`,`INSERT INTO hotel_usp_info (category, items) VALUES ('services', '${JSON.stringify(["室外泳池/儿童泳池","SPA","运动中心、健身房"])}')`],p=[...o,...n,...r,...u];for(const m of p)this.db.exec(m);this.db.exec("COMMIT"),this.save(),console.log(`✅ SQLite默认数据初始化完成 (${Date.now()-e}ms)`)}catch(o){throw this.db.exec("ROLLBACK"),o}}else console.log("✅ SQLite数据已存在，跳过初始化")}catch(e){throw console.error("❌ SQLite数据初始化失败:",e),e}}getInfo(){return{type:"SQLite",connection:this.dbPath,description:"SQLite文件数据库 - 适合开发和小型应用"}}}const K={type:"sqlite",mysql:{host:"127.0.0.1",port:3306,username:"root",password:"zy123good",database:"hiltonmarket"},sqlite:{storage:h.join(c.app.getPath("userData"),"hiltonmarket.db")}};let N={...K},w=null;const d=()=>N,C=t=>{N.type=t,console.log(`🔄 数据库类型切换为: ${t.toUpperCase()}`)},Z=t=>{N.mysql={...N.mysql,...t},console.log("🔧 MySQL配置已更新")},ee=t=>{N.sqlite={...N.sqlite,...t},console.log("🔧 SQLite配置已更新")},te=()=>{const t=d();if(t.type==="mysql")return console.log("🔗 连接MySQL数据库..."),new i.Sequelize({dialect:"mysql",host:t.mysql.host,port:t.mysql.port,username:t.mysql.username,password:t.mysql.password,database:t.mysql.database,logging:!1,pool:{max:3,min:1,acquire:1e4,idle:5e3},dialectOptions:{connectTimeout:5e3,acquireTimeout:5e3,timeout:5e3}});throw console.log("🔗 使用SQLite数据库（sql.js）..."),new Error("SQLite模式下不使用Sequelize，请使用SQLiteAdapter")},se=()=>w,j=async()=>{const t=d();t.type==="sqlite"&&(w=new Y(t.sqlite.storage),await w.init(),await w.initializeData())},M=()=>{w&&(w.close(),w=null)},A=()=>{const t=d();return t.type==="mysql"?{type:"MySQL",connection:`${t.mysql.host}:${t.mysql.port}/${t.mysql.database}`,description:"MySQL数据库 - 适合生产环境"}:{type:"SQLite",connection:t.sqlite.storage,description:"SQLite文件数据库 - 适合开发和小型应用"}},k=()=>{const t=process.env.DB_TYPE;t&&["mysql","sqlite"].includes(t)&&C(t),process.env.DB_HOST&&Z({host:process.env.DB_HOST,port:process.env.DB_PORT?parseInt(process.env.DB_PORT):3306,username:process.env.DB_USERNAME||"root",password:process.env.DB_PASSWORD||"",database:process.env.DB_DATABASE||"hiltonmarket"}),process.env.SQLITE_STORAGE&&ee({storage:process.env.SQLITE_STORAGE})},ae=async t=>{try{await t.authenticate();const s=A();return console.log(`✅ ${s.type}数据库连接成功`),console.log(`📍 连接地址: ${s.connection}`),!0}catch(s){const e=A();return console.error(`❌ ${e.type}数据库连接失败:`,s),!1}};class oe{get adapter(){const s=se();if(!s)throw new Error("SQLite适配器未初始化");return s}async getUsers(s={}){const{limit:e=10,offset:a=0,search:o=""}=s;let n="SELECT * FROM users",r="SELECT COUNT(*) as count FROM users";const u=[];if(o){const L=" WHERE username LIKE ? OR email LIKE ?";n+=L,r+=L,u.push(`%${o}%`,`%${o}%`)}n+=" ORDER BY id ASC LIMIT ? OFFSET ?",u.push(e,a);const p=this.adapter.all(n,u),m=this.adapter.get(r,o?[`%${o}%`,`%${o}%`]:[]);return{users:p,total:(m==null?void 0:m.count)||0}}async getUserById(s){return this.adapter.get("SELECT * FROM users WHERE id = ?",[s])}async createUser(s){const{username:e,email:a,role:o="user"}=s,n=this.adapter.run("INSERT INTO users (username, email, role) VALUES (?, ?, ?)",[e,a,o]);return this.getUserById(n.lastID)}async updateUser(s,e){const a=[],o=[];return e.username&&(a.push("username = ?"),o.push(e.username)),e.email&&(a.push("email = ?"),o.push(e.email)),e.role&&(a.push("role = ?"),o.push(e.role)),a.length===0?this.getUserById(s):(a.push("updatedAt = CURRENT_TIMESTAMP"),o.push(s),this.adapter.run(`UPDATE users SET ${a.join(", ")} WHERE id = ?`,o),this.getUserById(s))}async deleteUser(s){const e=this.getUserById(s);return this.adapter.run("DELETE FROM users WHERE id = ?",[s]),e}async getHotelBasicInfo(){return this.adapter.all("SELECT * FROM hotel_basic_info ORDER BY id ASC")}async updateHotelBasicInfo(s){this.adapter.run("DELETE FROM hotel_basic_info");for(const e of s)this.adapter.run("INSERT INTO hotel_basic_info (label, value, note) VALUES (?, ?, ?)",[e.label,e.value,e.note||null]);return this.getHotelBasicInfo()}async getHotelBudgetInfo(){return this.adapter.all("SELECT * FROM hotel_budget_info ORDER BY id ASC")}async updateHotelBudgetInfo(s){this.adapter.run("DELETE FROM hotel_budget_info");for(const e of s)this.adapter.run("INSERT INTO hotel_budget_info (label, value) VALUES (?, ?)",[e.label,e.value]);return this.getHotelBudgetInfo()}async getHotelUspInfo(){return this.adapter.all("SELECT * FROM hotel_usp_info ORDER BY id ASC").map(e=>({...e,items:JSON.parse(e.items)}))}async updateHotelUspInfo(s){if(this.adapter.run("DELETE FROM hotel_usp_info"),s.length>0){const e=s[0];for(const[a,o]of Object.entries(e))this.adapter.run("INSERT INTO hotel_usp_info (category, items) VALUES (?, ?)",[a,JSON.stringify(o)])}return this.getHotelUspInfo()}async getHotelInfo(){const s=await this.getHotelBasicInfo(),e=await this.getHotelBudgetInfo(),o=(await this.getHotelUspInfo()).reduce((n,r)=>(n[r.category]=r.items,n),{});return{basicInfo:s,budgetInfo:e,uspData:[o]}}async checkTablesExist(){return this.adapter.all(`
      SELECT name FROM sqlite_master
      WHERE type = 'table'
      AND name IN ('users', 'hotel_basic_info', 'hotel_budget_info', 'hotel_usp_info')
    `).length===4}async checkNeedsInitialization(){var e;return(((e=this.adapter.get("SELECT COUNT(*) as count FROM users"))==null?void 0:e.count)||0)===0}}const y=new oe;let f;const P=async()=>{if(k(),f=te(),!await ae(f))throw new Error("数据库连接验证失败");return f},q=()=>{if(d().type==="sqlite")throw new Error("SQLite模式下不使用Sequelize，请使用SQLiteAdapter");if(!f)throw new Error("数据库未初始化，请先调用 initSequelize()");return f},ne=async t=>{console.log(`🔄 正在切换到 ${t.toUpperCase()} 数据库...`),f&&await f.close(),d().type==="sqlite"&&M(),C(t),t==="sqlite"?(await j(),f=null):(f=await P(),B()),console.log(`✅ 已成功切换到 ${t.toUpperCase()} 数据库`)};class g extends i.Model{}class b extends i.Model{}class E extends i.Model{}class T extends i.Model{}const B=()=>{const t=q();g.init({id:{type:i.DataTypes.INTEGER.UNSIGNED,autoIncrement:!0,primaryKey:!0},username:{type:i.DataTypes.STRING(50),allowNull:!1,unique:!0},email:{type:i.DataTypes.STRING(100),allowNull:!1,unique:!0,validate:{isEmail:!0}},role:{type:i.DataTypes.ENUM("admin","user"),allowNull:!1,defaultValue:"user"}},{sequelize:t,tableName:"users",timestamps:!0}),b.init({id:{type:i.DataTypes.INTEGER.UNSIGNED,autoIncrement:!0,primaryKey:!0},label:{type:i.DataTypes.STRING(100),allowNull:!1},value:{type:i.DataTypes.TEXT,allowNull:!1},note:{type:i.DataTypes.STRING(200),allowNull:!0}},{sequelize:t,tableName:"hotel_basic_info",timestamps:!0}),E.init({id:{type:i.DataTypes.INTEGER.UNSIGNED,autoIncrement:!0,primaryKey:!0},label:{type:i.DataTypes.STRING(100),allowNull:!1},value:{type:i.DataTypes.STRING(100),allowNull:!1}},{sequelize:t,tableName:"hotel_budget_info",timestamps:!0}),T.init({id:{type:i.DataTypes.INTEGER.UNSIGNED,autoIncrement:!0,primaryKey:!0},category:{type:i.DataTypes.STRING(50),allowNull:!1},items:{type:i.DataTypes.JSON,allowNull:!1}},{sequelize:t,tableName:"hotel_usp_info",timestamps:!0})},re=async()=>{const t=Date.now();console.log("🚀 开始数据库初始化...");try{k();const s=d(),e=A();if(console.log(`📊 当前使用: ${e.description}`),s.type==="mysql"){console.log("🔗 初始化MySQL数据库...");const a=Date.now();await P(),B(),console.log(`✅ MySQL连接建立 (${Date.now()-a}ms)`);const[o,n]=await Promise.all([ie(),le().catch(()=>!0)]);if(o)if(console.log("✅ 数据库表已存在，跳过同步"),n){const r=Date.now();await v(),console.log(`✅ 初始数据补充完成 (${Date.now()-r}ms)`)}else console.log("✅ 数据已存在，跳过初始化");else{console.log("🔧 首次运行，创建数据库表...");const r=Date.now();await q().sync({force:!1}),console.log(`✅ 数据库表创建完成 (${Date.now()-r}ms)`);const u=Date.now();await v(),console.log(`✅ 初始数据创建完成 (${Date.now()-u}ms)`)}}else console.log("🔗 初始化SQLite数据库..."),await j();console.log(`🎉 数据库初始化完成！总耗时: ${Date.now()-t}ms`)}catch(s){throw console.error(`❌ 数据库初始化失败 (耗时: ${Date.now()-t}ms):`,s),s}},ie=async()=>{try{if(d().type==="mysql"){const s=q(),[e]=await s.query(`
        SELECT COUNT(*) as count
        FROM information_schema.tables
        WHERE table_schema = 'hiltonmarket'
        AND table_name IN ('users', 'hotel_basic_info', 'hotel_budget_info', 'hotel_usp_info')
      `);return e[0].count===4}else return await y.checkTablesExist()}catch{return!1}},le=async()=>{try{return d().type==="mysql"?await g.count()===0:await y.checkNeedsInitialization()}catch{return!0}},v=async()=>{try{await f.transaction(async t=>{await Promise.all([g.bulkCreate([{username:"admin",email:"<EMAIL>",role:"admin"},{username:"user",email:"<EMAIL>",role:"user"}],{transaction:t,ignoreDuplicates:!0}),b.bulkCreate([{label:"计划制定人",value:"Alice",note:"用户手填"},{label:"酒店In Code",value:"AOGCN",note:"incode预处理数据"},{label:"酒店名称",value:"九寨沟康莱德酒店",note:"预填与incode匹配"},{label:"酒店所在区域",value:"西区",note:"预填与incode匹配"},{label:"总经理",value:"A",note:"用户手填"},{label:"商务总监",value:"B",note:"用户手填"},{label:"市场总监",value:"C",note:"用户手填"},{label:"MECC 联系人",value:"D",note:"用户手填"}],{transaction:t,ignoreDuplicates:!0}),E.bulkCreate([{label:"酒店本地活动预算",value:"¥620,125.00"},{label:"集团市场共享费（Co-op Fund）",value:"¥349,561.33"},{label:"PMP",value:"¥60,000.00"},{label:"总预算",value:"¥1,029,686.33"}],{transaction:t,ignoreDuplicates:!0}),T.bulkCreate([{category:"rooms",items:["家庭房","景观房","独特风格房型","亲子房"]},{category:"dining",items:["免费早餐","餐厅拥有佳景","国际美食"]},{category:"meeting",items:["1000平米无柱宴会厅","40平米高清LED","10种会议室组合"]},{category:"services",items:["室外泳池/儿童泳池","SPA","运动中心、健身房"]}],{transaction:t,ignoreDuplicates:!0})])}),console.log("📝 默认数据初始化完成")}catch(t){throw console.error("❌ 初始化数据失败:",t),t}},ce=async()=>{try{d().type==="mysql"?f&&(await f.close(),console.log("✅ MySQL数据库连接已关闭")):(M(),console.log("✅ SQLite数据库已关闭"))}catch(t){console.error("❌ 关闭数据库连接失败:",t)}};let O=null,I=8e3;const S={async getUsers(t){if(d().type==="mysql"){const{page:e,limit:a,search:o}=t,n=(e-1)*a,r={};o&&(r[i.Op.or]=[{username:{[i.Op.like]:`%${o}%`}},{email:{[i.Op.like]:`%${o}%`}}]);const{count:u,rows:p}=await g.findAndCountAll({where:r,limit:a,offset:n,order:[["id","ASC"]],attributes:["id","username","email","role","createdAt","updatedAt"]});return{users:p.map(m=>m.toJSON()),total:u}}else return await y.getUsers({limit:t.limit,offset:(t.page-1)*t.limit,search:t.search})},async getUserById(t){if(d().type==="mysql"){const e=await g.findByPk(t,{attributes:["id","username","email","role","createdAt","updatedAt"]});return e?e.toJSON():null}else return await y.getUserById(t)},async createUser(t){return d().type==="mysql"?(await g.create(t)).toJSON():await y.createUser(t)},async updateUser(t,s){if(d().type==="mysql"){const a=await g.findByPk(t);return a?(await a.update(s),a.toJSON()):null}else return await y.updateUser(t,s)},async deleteUser(t){if(d().type==="mysql"){const e=await g.findByPk(t);if(!e)return null;const a=e.toJSON();return await e.destroy(),a}else return await y.deleteUser(t)},async getHotelInfo(){if(d().type==="mysql"){const s=await b.findAll({attributes:["label","value","note"],order:[["id","ASC"]]}),e=await E.findAll({attributes:["label","value"],order:[["id","ASC"]]}),o=(await T.findAll({attributes:["category","items"],order:[["id","ASC"]]})).reduce((n,r)=>(n[r.category]=r.items,n),{});return{basicInfo:s.map(n=>n.toJSON()),budgetInfo:e.map(n=>n.toJSON()),uspData:[o]}}else return await y.getHotelInfo()},async getHotelBasicInfo(){return d().type==="mysql"?(await b.findAll({attributes:["label","value","note"],order:[["id","ASC"]]})).map(e=>e.toJSON()):await y.getHotelBasicInfo()},async updateHotelBasicInfo(t){return d().type==="mysql"?(await b.destroy({where:{}}),(await b.bulkCreate(t)).map(a=>a.toJSON())):await y.updateHotelBasicInfo(t)}},Q=t=>new Promise(s=>{const a=require("net").createServer();a.listen(t,()=>{var n;const o=(n=a.address())==null?void 0:n.port;a.close(()=>{s(o||t)})}),a.on("error",()=>{Q(t+1).then(s)})}),ue=async()=>{try{await re(),I=await Q(8e3);const t=D();if(t.use(W()),t.use(D.json()),t.use(D.urlencoded({extended:!0})),c.app.isPackaged){const s=h.join(process.resourcesPath,"uploads");t.use("/uploads",D.static(s))}else t.use("/uploads",D.static(h.join(__dirname,"../../server/uploads")));return t.get("/health",(s,e)=>{const a=A();e.json({code:0,message:"success",data:{status:"OK",message:"Electron API Server is running",timestamp:new Date().toISOString(),port:I,database:{type:a.type,connection:a.connection,description:a.description}}})}),t.get("/database/info",(s,e)=>{try{const a=A();e.json({code:0,message:"success",data:a})}catch(a){console.error("获取数据库信息失败:",a),e.status(500).json({code:500,message:"获取数据库信息失败"})}}),t.post("/database/switch",async(s,e)=>{try{const{type:a}=s.body;if(!a||!["mysql","sqlite"].includes(a))return e.status(400).json({code:400,message:"无效的数据库类型，支持: mysql, sqlite"});await ne(a);const o=A();e.json({code:0,message:`成功切换到 ${a.toUpperCase()} 数据库`,data:o})}catch(a){console.error("切换数据库失败:",a),e.status(500).json({code:500,message:"切换数据库失败: "+a.message})}}),t.get("/mock/analysis/total",(s,e)=>{e.json({code:0,data:{users:102400,messages:81212,moneys:9280,shoppings:13600}})}),t.get("/mock/analysis/userAccessSource",(s,e)=>{e.json({code:0,data:[{value:1e3,name:"analysis.directAccess"},{value:310,name:"analysis.mailMarketing"},{value:234,name:"analysis.allianceAdvertising"},{value:135,name:"analysis.videoAdvertising"},{value:1548,name:"analysis.searchEngines"}]})}),t.get("/mock/analysis/weeklyUserActivity",(s,e)=>{e.json({code:0,data:[{value:13253,name:"analysis.monday"},{value:34235,name:"analysis.tuesday"},{value:26321,name:"analysis.wednesday"},{value:12340,name:"analysis.thursday"},{value:24643,name:"analysis.friday"},{value:1322,name:"analysis.saturday"},{value:1324,name:"analysis.sunday"}]})}),t.get("/mock/analysis/monthlySales",(s,e)=>{e.json({code:0,data:[{estimate:100,actual:120,name:"analysis.january"},{estimate:120,actual:82,name:"analysis.february"},{estimate:161,actual:91,name:"analysis.march"},{estimate:134,actual:154,name:"analysis.april"},{estimate:105,actual:162,name:"analysis.may"},{estimate:160,actual:140,name:"analysis.june"},{estimate:165,actual:145,name:"analysis.july"},{estimate:114,actual:250,name:"analysis.august"},{estimate:163,actual:134,name:"analysis.september"},{estimate:185,actual:56,name:"analysis.october"},{estimate:118,actual:99,name:"analysis.november"},{estimate:123,actual:123,name:"analysis.december"}]})}),t.get("/mock/workplace/total",(s,e)=>{e.json({code:0,data:{project:40,access:2340,todo:10}})}),t.get("/mock/workplace/project",(s,e)=>{e.json({code:0,data:[{name:"Github",icon:"akar-icons:github-fill",message:"workplace.introduction",personal:"Archer",time:new Date},{name:"Vue",icon:"logos:vue",message:"workplace.introduction",personal:"Archer",time:new Date},{name:"Angular",icon:"logos:angular-icon",message:"workplace.introduction",personal:"Archer",time:new Date},{name:"React",icon:"logos:react",message:"workplace.introduction",personal:"Archer",time:new Date},{name:"Webpack",icon:"logos:webpack",message:"workplace.introduction",personal:"Archer",time:new Date},{name:"Vite",icon:"vscode-icons:file-type-vite",message:"workplace.introduction",personal:"Archer",time:new Date}]})}),t.get("/mock/workplace/dynamic",(s,e)=>{e.json({code:0,data:[{keys:["workplace.push","Github"],time:new Date},{keys:["workplace.push","Github"],time:new Date},{keys:["workplace.push","Github"],time:new Date},{keys:["workplace.push","Github"],time:new Date},{keys:["workplace.push","Github"],time:new Date},{keys:["workplace.push","Github"],time:new Date}]})}),t.get("/mock/workplace/team",(s,e)=>{e.json({code:0,data:[{name:"Github",icon:"akar-icons:github-fill"},{name:"Vue",icon:"logos:vue"},{name:"Angular",icon:"logos:angular-icon"},{name:"React",icon:"logos:react"},{name:"Webpack",icon:"logos:webpack"},{name:"Vite",icon:"vscode-icons:file-type-vite"}]})}),t.get("/mock/workplace/radar",(s,e)=>{e.json({code:0,data:[{name:"workplace.quote",max:65,personal:42,team:50},{name:"workplace.contribution",max:160,personal:30,team:140},{name:"workplace.hot",max:300,personal:20,team:28},{name:"workplace.yield",max:130,personal:35,team:35},{name:"workplace.follow",max:100,personal:80,team:90}]})}),t.get("/hotel/info",async(s,e)=>{try{const a=await S.getHotelInfo();e.json({code:0,message:"success",data:a})}catch(a){console.error("获取酒店信息失败:",a),e.status(500).json({code:500,message:"获取酒店信息失败"})}}),t.get("/hotel/basic",async(s,e)=>{try{const a=await S.getHotelBasicInfo();e.json({code:0,message:"success",data:a})}catch(a){console.error("获取酒店基本信息失败:",a),e.status(500).json({code:500,message:"获取酒店基本信息失败"})}}),t.get("/hotel/budget",async(s,e)=>{try{const a=await E.findAll({attributes:["label","value"],order:[["id","ASC"]]});e.json({code:0,message:"success",data:a.map(o=>o.toJSON())})}catch(a){console.error("获取酒店预算信息失败:",a),e.status(500).json({code:500,message:"获取酒店预算信息失败"})}}),t.get("/hotel/usp",async(s,e)=>{try{const o=(await T.findAll({attributes:["category","items"],order:[["id","ASC"]]})).reduce((n,r)=>(n[r.category]=r.items,n),{});e.json({code:0,message:"success",data:[o]})}catch(a){console.error("获取酒店USP信息失败:",a),e.status(500).json({code:500,message:"获取酒店USP信息失败"})}}),t.put("/hotel/basic",async(s,e)=>{try{const{basicInfo:a}=s.body;if(!a||!Array.isArray(a))return e.status(400).json({code:400,message:"无效的数据格式"});const o=await S.updateHotelBasicInfo(a);e.json({code:0,message:"酒店基本信息更新成功",data:o})}catch(a){console.error("更新酒店基本信息失败:",a),e.status(500).json({code:500,message:"更新酒店基本信息失败"})}}),t.put("/hotel/budget",async(s,e)=>{try{const{budgetInfo:a}=s.body;if(!a||!Array.isArray(a))return e.status(400).json({code:400,message:"无效的数据格式"});if(d().type==="mysql"){await E.destroy({where:{}});const n=await E.bulkCreate(a);e.json({code:0,message:"酒店预算信息更新成功",data:n.map(r=>r.toJSON())})}else{await y.updateHotelBudgetInfo(a);const n=await y.getHotelBudgetInfo();e.json({code:0,message:"酒店预算信息更新成功",data:n})}}catch(a){console.error("更新酒店预算信息失败:",a),e.status(500).json({code:500,message:"更新酒店预算信息失败"})}}),t.put("/hotel/usp",async(s,e)=>{try{const{uspData:a}=s.body;if(!a||!Array.isArray(a))return e.status(400).json({code:400,message:"无效的数据格式"});if(d().type==="mysql"){await T.destroy({where:{}});const n=[];if(a.length>0){const u=a[0];for(const[p,m]of Object.entries(u))n.push({category:p,items:JSON.stringify(m)})}const r=await T.bulkCreate(n);e.json({code:0,message:"酒店USP信息更新成功",data:r.map(u=>u.toJSON())})}else{await y.updateHotelUspInfo(a);const n=await y.getHotelUspInfo();e.json({code:0,message:"酒店USP信息更新成功",data:n})}}catch(a){console.error("更新酒店USP信息失败:",a),e.status(500).json({code:500,message:"更新酒店USP信息失败"})}}),t.put("/hotel/info",async(s,e)=>{try{const{basicInfo:a,budgetInfo:o,uspData:n}=s.body;if(!a||!o||!n)return e.status(400).json({code:400,message:"缺少必要的数据字段"});if(a&&Array.isArray(a)&&await S.updateHotelBasicInfo(a),o&&Array.isArray(o)&&(d().type==="mysql"?(await E.destroy({where:{}}),await E.bulkCreate(o)):await y.updateHotelBudgetInfo(o)),n&&Array.isArray(n))if(d().type==="mysql"){await T.destroy({where:{}});const p=[];if(n.length>0){const m=n[0];for(const[L,V]of Object.entries(m))p.push({category:L,items:JSON.stringify(V)})}await T.bulkCreate(p)}else await y.updateHotelUspInfo(n);const r=await S.getHotelInfo();e.json({code:0,message:"酒店信息更新成功",data:r})}catch(a){console.error("更新酒店信息失败:",a),e.status(500).json({code:500,message:"更新酒店信息失败"})}}),t.get("/users",async(s,e)=>{try{const{page:a=1,limit:o=10,search:n=""}=s.query,r=Number(a),u=Number(o),p=await S.getUsers({page:r,limit:u,search:n});e.json({code:0,message:"success",data:{users:p.users,total:p.total,page:r,limit:u}})}catch(a){console.error("获取用户列表失败:",a),e.status(500).json({code:500,message:"获取用户列表失败"})}}),t.get("/users/:id",async(s,e)=>{try{const{id:a}=s.params,o=await g.findByPk(a,{attributes:["id","username","email","role","createdAt","updatedAt"]});o?e.json({code:0,message:"success",data:o.toJSON()}):e.status(404).json({code:404,message:"用户不存在"})}catch(a){console.error("获取用户失败:",a),e.status(500).json({code:500,message:"获取用户失败"})}}),t.post("/users",async(s,e)=>{try{const{username:a,email:o,role:n="user"}=s.body;if(!a||!o)return e.status(400).json({code:400,message:"用户名和邮箱是必填项"});if(await g.findOne({where:{[i.Op.or]:[{username:a},{email:o}]}}))return e.status(400).json({code:400,message:"用户名或邮箱已存在"});const u=await g.create({username:a,email:o,role:n});e.status(201).json({code:0,message:"用户创建成功",data:u.toJSON()})}catch(a){console.error("创建用户失败:",a),e.status(500).json({code:500,message:"创建用户失败"})}}),t.put("/users/:id",async(s,e)=>{try{const{id:a}=s.params,{username:o,email:n,role:r}=s.body,u=await g.findByPk(a);if(!u)return e.status(404).json({code:404,message:"用户不存在"});if(o||n){const m={id:{[i.Op.ne]:a}};if(o&&n?m[i.Op.or]=[{username:o},{email:n}]:o?m.username=o:n&&(m.email=n),await g.findOne({where:m}))return e.status(400).json({code:400,message:"用户名或邮箱已被其他用户使用"})}const p={};o&&(p.username=o),n&&(p.email=n),r&&(p.role=r),await u.update(p),e.json({code:0,message:"用户更新成功",data:u.toJSON()})}catch(a){console.error("更新用户失败:",a),e.status(500).json({code:500,message:"更新用户失败"})}}),t.delete("/users/:id",async(s,e)=>{try{const{id:a}=s.params,o=await g.findByPk(a);if(!o)return e.status(404).json({code:404,message:"用户不存在"});const n=o.toJSON();await o.destroy(),e.json({code:0,message:"用户删除成功",data:n})}catch(a){console.error("删除用户失败:",a),e.status(500).json({code:500,message:"删除用户失败"})}}),t.use((s,e,a,o)=>{console.error("API Error:",s.stack),a.status(500).json({code:500,message:"服务器内部错误"})}),t.use("*",(s,e)=>{e.status(404).json({code:404,message:"接口不存在"})}),new Promise((s,e)=>{O=t.listen(I,"127.0.0.1",()=>{console.log(`🚀 Electron API Server started on http://localhost:${I}`),s(I)}),O.on("error",a=>{console.error("Server start error:",a),e(a)})})}catch(t){throw console.error("Failed to start API server:",t),t}},de=()=>new Promise(async t=>{try{await ce(),O?O.close(()=>{console.log("🛑 API Server stopped"),O=null,t()}):t()}catch(s){console.error("停止API服务器时出错:",s),t()}}),U=()=>I;process.env.DIST_ELECTRON=h.join(__dirname,"..");process.env.DIST=h.join(process.env.DIST_ELECTRON,"../dist");process.env.PUBLIC=c.app.isPackaged?process.env.DIST:h.join(process.env.DIST_ELECTRON,"../public");z.release().startsWith("6.1")&&c.app.disableHardwareAcceleration();process.platform==="win32"&&c.app.setAppUserModelId(c.app.getName());c.app.requestSingleInstanceLock()||(c.app.quit(),process.exit(0));let l=null;const F=h.join(__dirname,"../preload/index.js"),H=process.env.VITE_DEV_SERVER_URL,x=h.join(process.env.DIST,"index.html");async function $(){try{const t=await ue();console.log(`✅ API Server started on port ${t}`)}catch(t){console.error("❌ Failed to start API server:",t)}l=new c.BrowserWindow({title:"菜单管理工具",icon:h.join(process.env.PUBLIC,"favicon.ico"),webPreferences:{preload:F,nodeIntegration:!0,contextIsolation:!1},frame:!0}),process.env.VITE_DEV_SERVER_URL?l.loadURL(H):l.loadFile(x),l.maximize(),l.webContents.on("did-finish-load",()=>{l==null||l.webContents.send("main-process-message",new Date().toLocaleString()),l==null||l.webContents.send("api-server-port",U())}),l.webContents.setWindowOpenHandler(({url:t})=>(t.startsWith("https:")&&c.shell.openExternal(t),{action:"deny"}))}c.app.whenReady().then($);c.app.on("window-all-closed",async()=>{try{await de(),console.log("✅ API Server stopped")}catch(t){console.error("❌ Failed to stop API server:",t)}l=null,process.platform!=="darwin"&&c.app.quit()});c.app.on("second-instance",()=>{l&&(l.isMinimized()&&l.restore(),l.focus())});c.app.on("activate",()=>{const t=c.BrowserWindow.getAllWindows();t.length?t[0].focus():$()});c.ipcMain.handle("open-win",(t,s)=>{const e=new c.BrowserWindow({webPreferences:{preload:F,nodeIntegration:!0,contextIsolation:!1}});c.app.isPackaged?e.loadFile(x,{hash:s}):e.loadURL(`${H}#${s}`)});c.ipcMain.on("window-min",function(){l&&l.minimize()});c.ipcMain.on("window-max",function(){l&&(l.isMaximized()?l.restore():l.maximize())});c.ipcMain.on("window-close",function(){l&&l.close()});c.ipcMain.handle("get-api-port",()=>U());c.ipcMain.handle("get-api-status",()=>({port:U(),url:`http://localhost:${U()}`,status:"running"}));
